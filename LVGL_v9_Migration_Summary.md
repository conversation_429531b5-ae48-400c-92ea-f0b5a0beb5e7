# LVGL v9 迁移总结

## 主要API变更

### 1. 显示驱动API变更
**v8 API:**
```cpp
static lv_disp_draw_buf_t draw_buf;
static lv_disp_drv_t disp_drv;

lv_disp_draw_buf_init(&draw_buf, buf1, buf2, screenWidth * 200);
lv_disp_drv_init(&disp_drv);
disp_drv.hor_res = screenWidth;
disp_drv.ver_res = screenHeight;
disp_drv.flush_cb = my_disp_flush;
disp_drv.draw_buf = &draw_buf;
lv_disp_drv_register(&disp_drv);
```

**v9 API:**
```cpp
static lv_display_t *display;

display = lv_display_create(screenWidth, screenHeight);
lv_display_set_flush_cb(display, my_disp_flush);
// 注意：v9中缓冲区大小以字节为单位
lv_display_set_buffers(display, buf1, buf2, sizeof(lv_color_t) * screenWidth * 200, LV_DISPLAY_RENDER_MODE_PARTIAL);
```

### 2. 输入设备API变更
**v8 API:**
```cpp
static lv_indev_drv_t indev_drv;
lv_indev_drv_init(&indev_drv);
indev_drv.type = LV_INDEV_TYPE_POINTER;
indev_drv.read_cb = my_touchpad_read;
lv_indev_drv_register(&indev_drv);
```

**v9 API:**
```cpp
lv_indev_t *indev = lv_indev_create();
lv_indev_set_type(indev, LV_INDEV_TYPE_POINTER);
lv_indev_set_read_cb(indev, my_touchpad_read);
```

### 3. 回调函数签名变更
**显示刷新回调:**
```cpp
// v8
void my_disp_flush(lv_disp_drv_t *disp, const lv_area_t *area, lv_color_t *color_p)

// v9
void my_disp_flush(lv_display_t *disp, const lv_area_t *area, lv_color_t *color_p)
```

**触摸输入回调:**
```cpp
// v8
void my_touchpad_read(lv_indev_drv_t *indev_driver, lv_indev_data_t *data)

// v9
void my_touchpad_read(lv_indev_t *indev, lv_indev_data_t *data)
```

### 4. 函数名称变更
- `lv_btn_create()` → `lv_button_create()`
- `lv_scr_act()` → `lv_screen_active()`
- `lv_disp_flush_ready()` → `lv_display_flush_ready()`
- `LV_INDEV_STATE_REL` → `LV_INDEV_STATE_RELEASED`
- `LV_INDEV_STATE_PR` → `LV_INDEV_STATE_PRESSED`

### 5. 头文件包含
```cpp
#include <demos/lv_demos.h>  // LVGL v9 演示头文件
```

## 重要注意事项
1. **缓冲区大小单位变更**: v9中`lv_display_set_buffers()`的缓冲区大小参数以字节为单位，而不是像素
2. **颜色格式**: v9中`lv_color_t`始终是RGB888格式，不受`LV_COLOR_DEPTH`影响
3. **配置文件**: 需要使用v9的`lv_conf_template.h`更新`lv_conf.h`
4. **演示函数**: 确保在`lv_conf.h`中启用了演示功能

## 编译前检查清单
- [ ] 更新`lv_conf.h`配置文件
- [ ] 确保包含正确的头文件
- [ ] 验证所有API调用已更新
- [ ] 检查缓冲区大小计算
- [ ] 确认演示功能已启用
