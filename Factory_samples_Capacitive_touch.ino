#include <lvgl.h>
#include <TFT_eSPI.h>
#include "CST820.h"
// 注释掉demos头文件，使用自定义演示函数
// #include <demos/lv_demos.h>  // LVGL v9 演示头文件 - 暂时注释掉避免编译错误

/*更改屏幕分辨率*/
static const uint16_t screenWidth = 240;
static const uint16_t screenHeight = 320;

/*定义触摸屏引脚*/
#define I2C_SDA 33
#define I2C_SCL 32
#define TP_RST 25
#define TP_INT 21

// LVGL v9 显示相关变量
static lv_display_t *display;
static lv_color_t *buf1;
static lv_color_t *buf2;

TFT_eSPI tft = TFT_eSPI();                      /* TFT实例 */
CST820 touch(I2C_SDA, I2C_SCL, TP_RST, TP_INT); /* 触摸实例 */

#if LV_USE_LOG != 0
/* 串行调试 */
void my_print(const char *buf)
{
    Serial.printf(buf);
    Serial.flush();
}
#endif
//_______________________
void lv_example_btn(void)
{
    /*要转换的属性*/
    static lv_style_prop_t props[] = {
        LV_STYLE_TRANSFORM_WIDTH, LV_STYLE_TRANSFORM_HEIGHT, LV_STYLE_TEXT_LETTER_SPACE};

    /*Transition descriptor when going back to the default state.
     *Add some delay to be sure the press transition is visible even if the press was very short*/
    static lv_style_transition_dsc_t transition_dsc_def;
    lv_style_transition_dsc_init(&transition_dsc_def, props, lv_anim_path_overshoot, 250, 100, NULL);

    /*Transition descriptor when going to pressed state.
     *No delay, go to presses state immediately*/
    static lv_style_transition_dsc_t transition_dsc_pr;
    lv_style_transition_dsc_init(&transition_dsc_pr, props, lv_anim_path_ease_in_out, 250, 0, NULL);

    /*Add only the new transition to he default state*/
    static lv_style_t style_def;
    lv_style_init(&style_def);
    lv_style_set_transition(&style_def, &transition_dsc_def);

    /*Add the transition and some transformation to the presses state.*/
    static lv_style_t style_pr;
    lv_style_init(&style_pr);
    lv_style_set_transform_width(&style_pr, 10);
    lv_style_set_transform_height(&style_pr, -10);
    lv_style_set_text_letter_space(&style_pr, 10);
    lv_style_set_transition(&style_pr, &transition_dsc_pr);

    lv_obj_t *btn1 = lv_button_create(lv_screen_active());
    lv_obj_align(btn1, LV_ALIGN_CENTER, 0, -80);
    lv_obj_add_style(btn1, &style_pr, LV_STATE_PRESSED);
    lv_obj_add_style(btn1, &style_def, 0);

    lv_obj_t *label = lv_label_create(btn1);
    lv_label_set_text(label, "btn1");

    /*Init the style for the default state*/
    static lv_style_t style;
    lv_style_init(&style);

    lv_style_set_radius(&style, 3);

    lv_style_set_bg_opa(&style, LV_OPA_100);
    lv_style_set_bg_color(&style, lv_palette_main(LV_PALETTE_BLUE));
    lv_style_set_bg_grad_color(&style, lv_palette_darken(LV_PALETTE_BLUE, 2));
    lv_style_set_bg_grad_dir(&style, LV_GRAD_DIR_VER);

    lv_style_set_border_opa(&style, LV_OPA_40);
    lv_style_set_border_width(&style, 2);
    lv_style_set_border_color(&style, lv_palette_main(LV_PALETTE_GREY));

    lv_style_set_shadow_width(&style, 8);
    lv_style_set_shadow_color(&style, lv_palette_main(LV_PALETTE_GREY));
    lv_style_set_shadow_ofs_y(&style, 8);

    lv_style_set_outline_opa(&style, LV_OPA_COVER);
    lv_style_set_outline_color(&style, lv_palette_main(LV_PALETTE_BLUE));

    lv_style_set_text_color(&style, lv_color_white());
    lv_style_set_pad_all(&style, 10);

    /*Init the pressed style*/
    static lv_style_t style_pr_2;
    lv_style_init(&style_pr_2);

    /*Ad a large outline when pressed*/
    lv_style_set_outline_width(&style_pr_2, 30);
    lv_style_set_outline_opa(&style_pr_2, LV_OPA_TRANSP);

    lv_style_set_translate_y(&style_pr_2, 5);
    lv_style_set_shadow_ofs_y(&style_pr_2, 3);
    lv_style_set_bg_color(&style_pr_2, lv_palette_darken(LV_PALETTE_BLUE, 2));
    lv_style_set_bg_grad_color(&style_pr_2, lv_palette_darken(LV_PALETTE_BLUE, 4));

    /*Add a transition to the the outline*/
    static lv_style_transition_dsc_t trans;
    static lv_style_prop_t props2[] = {LV_STYLE_OUTLINE_WIDTH, LV_STYLE_OUTLINE_OPA};
    lv_style_transition_dsc_init(&trans, props2, lv_anim_path_linear, 300, 0, NULL);

    lv_style_set_transition(&style_pr_2, &trans);

    lv_obj_t *btn2 = lv_button_create(lv_screen_active());
    lv_obj_remove_style_all(btn2); /*Remove the style coming from the theme*/
    lv_obj_add_style(btn2, &style, 0);
    lv_obj_add_style(btn2, &style_pr_2, LV_STATE_PRESSED);
    lv_obj_set_size(btn2, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
    lv_obj_center(btn2);

    lv_obj_t *label2 = lv_label_create(btn2);
    lv_label_set_text(label2, "Button");
    lv_obj_center(label2);
}
//_______________________
/* 显示器刷新 - LVGL v9 API */
void my_disp_flush(lv_display_t *disp, const lv_area_t *area, uint8_t *px_map)
{
    uint32_t w = (area->x2 - area->x1 + 1);
    uint32_t h = (area->y2 - area->y1 + 1);

    //tft.startWrite();
    tft.pushImageDMA(area->x1, area->y1, w, h, (uint16_t *)px_map);
    //tft.endWrite();

    lv_display_flush_ready(disp);
}

/*读取触摸板 - LVGL v9 API */
void my_touchpad_read(lv_indev_t *indev, lv_indev_data_t *data)
{
    bool touched;
    uint8_t gesture;
    uint16_t touchX, touchY;

    touched = touch.getTouch(&touchX, &touchY, &gesture);

    if (!touched)
    {
        data->state = LV_INDEV_STATE_RELEASED;
    }
    else
    {
        data->state = LV_INDEV_STATE_PRESSED;

        /*Set the coordinates*/
        data->point.x = touchX;
        data->point.y = touchY;
    }
}

void setup()
{
    Serial.begin(115200); /*初始化串口*/

    String LVGL_Arduino = "Hello Arduino! ";
    LVGL_Arduino += String('V') + lv_version_major() + "." + lv_version_minor() + "." + lv_version_patch();

    Serial.println(LVGL_Arduino);
    Serial.println("I am LVGL_Arduino");

    lv_init();

#if LV_USE_LOG != 0
    lv_log_register_print_cb(my_print); /* 用于调试的注册打印功能 */
#endif
    pinMode(27, OUTPUT);
    digitalWrite(27, LOW);
    tft.begin();        /*初始化*/
    tft.setRotation(0); /* 旋转 */
    tft.initDMA();      /* 初始化DMA */

    touch.begin(); /*初始化触摸板*/
    digitalWrite(27, HIGH);
    tft.fillScreen(TFT_RED);
    delay(500);
    tft.fillScreen(TFT_GREEN);
    delay(500);
    tft.fillScreen(TFT_BLUE);
    delay(500);
    tft.fillScreen(TFT_BLACK);
    delay(500);

    // LVGL v9 显示初始化
    buf1 = (lv_color_t *)heap_caps_malloc(sizeof(lv_color_t) * screenWidth * 200, MALLOC_CAP_DMA | MALLOC_CAP_INTERNAL);
    buf2 = (lv_color_t *)heap_caps_malloc(sizeof(lv_color_t) * screenWidth * 200, MALLOC_CAP_DMA | MALLOC_CAP_INTERNAL);

    // 创建显示器
    display = lv_display_create(screenWidth, screenHeight);
    lv_display_set_flush_cb(display, my_disp_flush);
    // 注意：v9中缓冲区大小以字节为单位，而不是像素
    lv_display_set_buffers(display, buf1, buf2, sizeof(lv_color_t) * screenWidth * 200, LV_DISPLAY_RENDER_MODE_PARTIAL);

    // LVGL v9 输入设备初始化
    lv_indev_t *indev = lv_indev_create();
    lv_indev_set_type(indev, LV_INDEV_TYPE_POINTER);
    lv_indev_set_read_cb(indev, my_touchpad_read);

#if 0
    /* 创建简单标签 */
//    lv_obj_t *label = lv_label_create( lv_screen_active() );
//    lv_label_set_text( label, LVGL_Arduino.c_str() );
//    lv_obj_align( label, LV_ALIGN_CENTER, 0, 0 );
     lv_example_btn();
#else
    /* 尝试lv_examples Arduino库中的一个示例
       请确保按照上面所写的内容将其包括在内。
    lv_example_btn_1();
   */

    // 暂时注释掉官方demos，使用自定义演示
    // lv_demo_widgets(); // 需要正确配置demos文件夹后才能使用
    // lv_demo_benchmark(); // 需要正确配置demos文件夹后才能使用

    // 使用自定义演示函数替代
    lv_example_btn(); // 使用我们自己定义的按钮演示

    //  lv_demo_keypad_encoder();     // works, but I haven't an encoder
    //  lv_demo_music();              // NOK
    //  lv_demo_printer();
    //  lv_demo_stress();             // seems to be OK
#endif
    Serial.println("Setup done");
    tft.startWrite();
}

void loop()
{
    lv_timer_handler(); /* 让GUI完成它的工作 */
    delay(5);
}
