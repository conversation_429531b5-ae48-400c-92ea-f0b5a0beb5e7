# LVGL v9 Arduino Demos 编译错误修复指南

## 问题描述
编译时出现错误：
```
fatal error: demos/lv_demos.h: No such file or directory
```

## 问题原因
1. **Arduino构建系统限制**：Arduino的构建系统要求库文件必须在特定的目录结构中
2. **LVGL库结构问题**：通过Arduino库管理器安装的LVGL中，`demos`文件夹不在`src`目录下
3. **路径解析错误**：编译器无法找到正确的相对路径

## 解决方案

### 方案1：移动demos文件夹（推荐）

根据LVGL官方文档，需要手动复制demos文件夹：

1. **找到LVGL库位置**：
   - Windows: `C:\Users\<USER>\Documents\Arduino\libraries\lvgl\`
   - 或者在Arduino IDE中查看库的安装路径

2. **复制demos文件夹**：
   ```
   从: lvgl/demos/
   到: lvgl/src/demos/
   ```

3. **同样复制examples文件夹**（如果需要）：
   ```
   从: lvgl/examples/
   到: lvgl/src/examples/
   ```

4. **确保lv_conf.h配置正确**：
   ```cpp
   #define LV_USE_DEMO_WIDGETS 1
   #define LV_USE_DEMO_BENCHMARK 1
   // 其他demos根据需要启用
   ```

### 方案2：使用自定义演示（临时解决方案）

如果不想修改库文件，可以注释掉demos包含，使用自定义演示：

```cpp
// 注释掉这行
// #include <demos/lv_demos.h>

// 在setup()中使用自定义演示替代
// lv_demo_widgets(); // 注释掉
lv_example_btn(); // 使用自定义按钮演示
```

### 方案3：从GitHub下载完整版本

1. 从GitHub下载LVGL完整版本：https://github.com/lvgl/lvgl
2. 解压到Arduino libraries目录
3. 重命名文件夹为`lvgl`
4. 确保目录结构正确

## 验证修复

修复后，代码应该能够成功编译。可以使用以下测试代码验证：

```cpp
#include <lvgl.h>
#include <demos/lv_demos.h>  // 现在应该能找到

void setup() {
    // LVGL初始化代码...
    
    // 测试demos功能
    lv_demo_widgets();
}
```

## 常见问题

### Q: 移动文件夹后仍然报错？
A: 检查：
- 文件夹是否复制到正确位置
- lv_conf.h中是否启用了对应的demos
- Arduino IDE是否重启

### Q: 不想修改库文件怎么办？
A: 使用方案2，注释掉demos包含，使用自定义界面

### Q: 更新LVGL库后又出现问题？
A: 每次更新库后都需要重新复制demos文件夹

## 相关链接

- [LVGL Arduino集成文档](https://docs.lvgl.io/master/details/integration/framework/arduino.html)
- [LVGL GitHub Issues #6778](https://github.com/lvgl/lvgl/issues/6778)
- [LVGL论坛相关讨论](https://forum.lvgl.io/t/wt32-sc01-plus-arduino-v9-widget-demo-struggle/20641)

## 总结

这是LVGL v9在Arduino环境中的已知问题。推荐使用方案1（移动demos文件夹）作为永久解决方案，或使用方案2作为临时解决方案。
