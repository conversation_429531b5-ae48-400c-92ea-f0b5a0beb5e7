#include <lvgl.h>
#include <TFT_eSPI.h>
#include "CST820.h"
#include <demos/lv_demos.h>  // LVGL v9 演示头文件

/*更改屏幕分辨率*/
static const uint16_t screenWidth = 240;
static const uint16_t screenHeight = 320;

/*定义触摸屏引脚*/
#define I2C_SDA 33
#define I2C_SCL 32
#define TP_RST 25
#define TP_INT 21

// LVGL v9 显示相关变量
static lv_display_t *display;
static lv_color_t *buf1;
static lv_color_t *buf2;

TFT_eSPI tft = TFT_eSPI();                      /* TFT实例 */
CST820 touch(I2C_SDA, I2C_SCL, TP_RST, TP_INT); /* 触摸实例 */

#if LV_USE_LOG != 0
/* 串行调试 */
void my_print(const char *buf)
{
    Serial.printf(buf);
    Serial.flush();
}
#endif

/* 显示器刷新 - LVGL v9 API */
void my_disp_flush(lv_display_t *disp, const lv_area_t *area, lv_color_t *color_p)
{
    uint32_t w = (area->x2 - area->x1 + 1);
    uint32_t h = (area->y2 - area->y1 + 1);

    //tft.startWrite();
    tft.pushImageDMA(area->x1, area->y1, w, h, (uint16_t *)color_p);
    //tft.endWrite();

    lv_display_flush_ready(disp);
}

/*读取触摸板 - LVGL v9 API */
void my_touchpad_read(lv_indev_t *indev, lv_indev_data_t *data)
{
    bool touched;
    uint8_t gesture;
    uint16_t touchX, touchY;

    touched = touch.getTouch(&touchX, &touchY, &gesture);

    if (!touched)
    {
        data->state = LV_INDEV_STATE_RELEASED;
    }
    else
    {
        data->state = LV_INDEV_STATE_PRESSED;

        /*Set the coordinates*/
        data->point.x = touchX;
        data->point.y = touchY;
    }
}

void setup()
{
    Serial.begin(115200); /*初始化串口*/

    String LVGL_Arduino = "Hello Arduino! ";
    LVGL_Arduino += String('V') + lv_version_major() + "." + lv_version_minor() + "." + lv_version_patch();

    Serial.println(LVGL_Arduino);
    Serial.println("I am LVGL_Arduino");

    lv_init();

#if LV_USE_LOG != 0
    lv_log_register_print_cb(my_print); /* 用于调试的注册打印功能 */
#endif
    pinMode(27, OUTPUT);
    digitalWrite(27, LOW);
    tft.begin();        /*初始化*/
    tft.setRotation(0); /* 旋转 */
    tft.initDMA();      /* 初始化DMA */

    touch.begin(); /*初始化触摸板*/
    digitalWrite(27, HIGH);
    tft.fillScreen(TFT_RED);
    delay(500);
    tft.fillScreen(TFT_GREEN);
    delay(500);
    tft.fillScreen(TFT_BLUE);
    delay(500);
    tft.fillScreen(TFT_BLACK);
    delay(500);

    // LVGL v9 显示初始化
    buf1 = (lv_color_t *)heap_caps_malloc(sizeof(lv_color_t) * screenWidth * 200, MALLOC_CAP_DMA | MALLOC_CAP_INTERNAL);
    buf2 = (lv_color_t *)heap_caps_malloc(sizeof(lv_color_t) * screenWidth * 200, MALLOC_CAP_DMA | MALLOC_CAP_INTERNAL);

    // 创建显示器
    display = lv_display_create(screenWidth, screenHeight);
    lv_display_set_flush_cb(display, my_disp_flush);
    // 注意：v9中缓冲区大小以字节为单位，而不是像素
    lv_display_set_buffers(display, buf1, buf2, sizeof(lv_color_t) * screenWidth * 200, LV_DISPLAY_RENDER_MODE_PARTIAL);

    // LVGL v9 输入设备初始化
    lv_indev_t *indev = lv_indev_create();
    lv_indev_set_type(indev, LV_INDEV_TYPE_POINTER);
    lv_indev_set_read_cb(indev, my_touchpad_read);

    // 创建简单标签进行测试
    lv_obj_t *label = lv_label_create(lv_screen_active());
    lv_label_set_text(label, LVGL_Arduino.c_str());
    lv_obj_align(label, LV_ALIGN_CENTER, 0, -50);

    // 创建一个简单按钮
    lv_obj_t *btn = lv_button_create(lv_screen_active());
    lv_obj_align(btn, LV_ALIGN_CENTER, 0, 50);
    lv_obj_t *btn_label = lv_label_create(btn);
    lv_label_set_text(btn_label, "Test Button");
    lv_obj_center(btn_label);

    Serial.println("Setup done");
    tft.startWrite();
}

void loop()
{
    lv_timer_handler(); /* 让GUI完成它的工作 */
    delay(5);
}
